# main.py
"""
Ponto de entrada principal para a aplicação de simulação do sistema elétrico de potência.

Este módulo inicializa a configuração do sistema, carrega configurações do config.json,
cria geradores e o sistema elétrico de potência, e inicia a interface gráfica.
"""

import os
import sys
import json
import logging
import wx
import argparse
from typing import Dict, Any, List

# Definir o backend do matplotlib antes de importar quaisquer módulos relacionados ao matplotlib
import matplotlib
matplotlib.use('WXAgg')

# Importar nossos módulos
from core.generator import Generator, MODE_SYNCHRONOUS, MODE_DROOP
from core.simulation import PowerSystem, Simulation
from gui.gui_fixed import ElectricalBusGUI
from core.constants import SystemConfig
from plc_integration import setup_plc_communication

print("Todos os módulos importados com sucesso")

print("Configurando o registro de logs...")
# Configurar o registro de logs para arquivo
def setup_logging():
    """Configurar o registro de logs para redirecionar todas as declarações de impressão para um arquivo de log."""
    print("Dentro de setup_logging()")
    log_dir = "logs"
    if not os.path.exists(log_dir):
        print(f"Criando diretório {log_dir}")
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "simulation.log")
    print(f"Configurando arquivo de log: {log_file}")

    # Configurar o registro de logs
    logging.basicConfig(
        filename=log_file,
        level=logging.DEBUG,  # Alterado para DEBUG para capturar mensagens de debug
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Adicionar um manipulador de console para também mostrar logs no console
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)  # Mantém INFO para o console para não sobrecarregar
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    console.setFormatter(formatter)
    logging.getLogger('').addHandler(console)

    # Registrar a primeira mensagem no log
    logging.info("Sistema de registro de logs configurado")
    print("Registro de logs configurado")

print("Prestes a chamar setup_logging()")
setup_logging()
print("Finalizado setup_logging()")

# Função auxiliar para registrar mensagens no log e também imprimir no console
def log_message(message, level="info"):
    """
    Registra uma mensagem no log e também imprime no console.

    Args:
        message: Mensagem a ser registrada
        level: Nível de log (info, warning, error, debug)
    """
    # Sempre imprimir no console para manter compatibilidade
    print(message)

    # Registrar no sistema de log com o nível apropriado
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "warning":
        logging.warning(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "debug":
        logging.debug(message)
    else:
        logging.info(message)

# Configurações padrão dos geradores
DEFAULT_GENERATOR_CONFIGS = {
    "generator_A": {"name": "A", "mode": MODE_SYNCHRONOUS, "damping_factor": 16.0},
    "generator_B": {"name": "B", "mode": MODE_DROOP, "damping_factor": 6.0},
    "generator_C": {"name": "C", "mode": MODE_DROOP, "damping_factor": 6.0},
    "generator_D": {"name": "D", "mode": MODE_DROOP, "damping_factor": 6.0}
}

# Modos válidos dos geradores
VALID_GENERATOR_MODES = [MODE_SYNCHRONOUS, MODE_DROOP]

# Chaves obrigatórias dos geradores
REQUIRED_GENERATOR_KEYS = ["generator_A", "generator_B", "generator_C", "generator_D"]

def load_configuration() -> tuple[Dict[str, Dict[str, Any]], List[bool], SystemConfig, Dict[str, Any]]:
    """
    Carregar configuração do arquivo config.json.

    Returns:
        Tupla contendo:
        - Dicionário de configurações dos geradores
        - Lista de estados dos disjuntores
        - Objeto SystemConfig com valores atualizados
        - Dicionário com a configuração do PLC
    """
    config, config_data = load_config()

    try:
        # Extrair configurações dos geradores
        generator_configs = {
            f"generator_{gen['name']}": gen
            for gen in config_data.get("generators", [])
        }

        # Padronizar parâmetros da turbina para todos os geradores
        # Definir parâmetros padrão da turbina - idênticos para todos os geradores
        # Uma vez que todas as turbinas são do mesmo modelo do mesmo fabricante
        standard_turbine_params = {
            "H": 20.0,               # Constante de inércia padrão para este modelo de turbina
            "damping_factor": 30.0,   # Fator de amortecimento padrão para este modelo de turbina
            "K_pgov": 12.0,          # Ganho proporcional do governador padrão para este modelo de turbina
            "K_igov": 2.0,           # Ganho integral do governador padrão para este modelo de turbina
            "K_dgov": 0.8,           # Ganho derivativo do governador padrão para este modelo de turbina
            "K_turb": 1.2,           # Ganho da turbina padrão para este modelo de turbina
            "T_turb": 0.15,          # Constante de tempo da turbina padrão para este modelo de turbina
            "W_fnl": 0.15,           # Fluxo de combustível sem carga padrão para este modelo de turbina
            "disturb_start": 0.0,    # Sem perturbação por padrão
            "disturb_end": 0.0,      # Sem perturbação por padrão
            "disturb_value": 0.0     # Sem perturbação por padrão
        }

        # Verificar se há um gerador síncrono e se ele tem o maior setpoint de potência
        sync_gen_name = None
        sync_gen_setpoint = 0.0
        highest_non_sync_setpoint = 0.0

        # Primeira passagem: encontrar o gerador síncrono e o maior setpoint não-síncrono
        for _, gen in generator_configs.items():
            p_setpoint = gen.get("P_setpoint", 0.3)
            mode = gen.get("mode", "").lower()

            if mode == "synchronous":
                sync_gen_name = gen.get("name")
                sync_gen_setpoint = p_setpoint
            else:
                highest_non_sync_setpoint = max(highest_non_sync_setpoint, p_setpoint)

        # Verificar se o gerador síncrono tem o maior setpoint
        if sync_gen_name and sync_gen_setpoint <= highest_non_sync_setpoint:
            warning_message = f"\n{'*' * 80}\n"
            warning_message += f"AVISO: Gerador {sync_gen_name} (síncrono) não tem o maior setpoint de potência!\n"
            warning_message += f"Setpoint do gerador síncrono: {sync_gen_setpoint:.2f} pu\n"
            warning_message += f"Maior setpoint não-síncrono: {highest_non_sync_setpoint:.2f} pu\n"
            warning_message += "Para estabilidade ótima do sistema, o gerador síncrono deve ter\n"
            warning_message += "o maior setpoint de potência no sistema.\n"
            warning_message += "*" * 80 + "\n"

            # Registrar como um aviso no log
            logging.warning(warning_message)
            print(warning_message)

        # Aplicar parâmetros padrão a todos os geradores
        for gen in generator_configs.values():
            # Manter modo original e setpoints
            mode = gen.get("mode", "droop")
            p_setpoint = gen.get("P_setpoint", 0.3)
            v_setpoint = gen.get("V_setpoint", 1.0)

            # Aplicar todos os parâmetros padrão da turbina
            for param, value in standard_turbine_params.items():
                gen[param] = value

            # Restaurar modo original e setpoints
            gen["mode"] = mode
            gen["P_setpoint"] = p_setpoint
            gen["V_setpoint"] = v_setpoint

            print(f"Parâmetros padronizados da turbina para o gerador {gen.get('name', 'desconhecido')}")

        # Carregar estado dos disjuntores
        breaker_status = config_data.get("breaker_status", [True] * 8)
        if len(breaker_status) != 8:
            log_message("Aviso: comprimento de breaker_status inválido, usando padrão.", level="warning")
            breaker_status = [True] * 8

        print("Configurações carregadas de config.json.")

    except FileNotFoundError:
        log_message("Aviso: config.json não encontrado. Usando valores padrão.", level="warning")
        generator_configs = DEFAULT_GENERATOR_CONFIGS
        breaker_status = [True] * 8

    except json.JSONDecodeError as e:
        log_message(f"Erro: config.json malformado. Análise JSON falhou: {e}", level="error")
        generator_configs = DEFAULT_GENERATOR_CONFIGS
        breaker_status = [True] * 8

    except Exception as e:
        log_message(f"Erro: Erro inesperado ao carregar config.json: {e}", level="error")
        generator_configs = DEFAULT_GENERATOR_CONFIGS
        breaker_status = [True] * 8

    # Validate generator configurations
    generator_configs = validate_generator_configs(generator_configs)

    # Obter configuração do PLC
    plc_config = config_data.get("plc", {"enabled": False})

    return generator_configs, breaker_status, config, plc_config

def load_config() -> tuple[SystemConfig, dict]:
    """
    Carregar configuração do sistema a partir do config.json.

    Returns:
        Tupla contendo:
        - Objeto SystemConfig com a configuração do sistema
        - Dicionário com a configuração completa do arquivo config.json
    """
    try:
        # Try to load from root directory first
        config_path = 'config.json'
        if not os.path.exists(config_path):
            # If not found, try the config directory
            config_path = os.path.join('config', 'config.json')

        print(f"Carregando configuração de {config_path}")
        with open(config_path, 'r') as f:
            config_data = json.load(f)

        # Create the SystemConfig object
        config = SystemConfig.from_dict(config_data)

        # Update the config with bus data if available
        if 'bus_data' in config_data:
            # Legacy format with bus_data key
            update_system_config_from_bus_data(config, config_data['bus_data'])
        elif 'bus' in config_data:
            # New format with bus key
            update_system_config_from_bus_data(config, config_data['bus'])

        return config, config_data
    except FileNotFoundError:
        print("Arquivo de configuração não encontrado, usando valores padrão")
        return SystemConfig(s_base=30e6, v_base=13.8e3, f_base=60.0), {}
    except json.JSONDecodeError:
        print("Erro ao analisar arquivo de configuração, usando valores padrão")
        return SystemConfig(s_base=30e6, v_base=13.8e3, f_base=60.0), {}

def update_system_config_from_bus_data(config: SystemConfig, bus_data: Dict[str, Any]) -> None:
    """
    Atualizar SystemConfig com dados do barramento da configuração.

    Args:
        config: Objeto SystemConfig para atualizar
        bus_data: Dicionário contendo dados do barramento
    """
    print("\nAtualizando configuração do sistema a partir dos dados do barramento:")
    print(f"Dados do barramento: {bus_data}")

    # Inicializar dicionário de cargas
    config.loads = {}

    # Verificar se temos cargas individuais para cada barramento
    if "loads" in bus_data:
        # Usar cargas individuais para cada barramento
        bus_loads = bus_data.get("loads", {})
        for bus_id in ["A", "B", "C", "D"]:
            if bus_id in bus_loads:
                bus_load = bus_loads[bus_id]
                # Valores em pu (0.075 pu = 2.25MW/30MVA, 0.0363 pu = 1.089MVAR/30MVA)
                load_p_pu = bus_load.get("load_p", 0.075)  # Default to 0.075 pu
                load_q_pu = bus_load.get("load_q", 0.0363)  # Default to 0.0363 pu
                # Converter de pu para valores absolutos
                load_p = load_p_pu * config.s_base  # Converter para W
                load_q = load_q_pu * config.s_base  # Converter para VAr
                config.loads[bus_id] = {"P": load_p, "Q": load_q}
            else:
                # Valores padrão se o barramento não for especificado (em pu convertidos para W e VAr)
                config.loads[bus_id] = {"P": 0.075 * config.s_base, "Q": 0.0363 * config.s_base}
    else:
        # Verificar se load_p e load_q estão diretamente em bus_data (formato antigo)
        if "load_p" in bus_data:
            # Obter os valores de carga (agora em pu)
            load_p_pu = bus_data.get("load_p", 0.075)  # Em pu (2.25MW/30MVA = 0.075pu)
            load_q_pu = bus_data.get("load_q", 0.0363)  # Em pu (1.089MVAR/30MVA = 0.0363pu)

            # Converter de pu para watts
            load_p = load_p_pu * config.s_base  # Converter de pu para W
            load_q = load_q_pu * config.s_base  # Converter de pu para VAr

            # Calcular valores em MW e MVAR para exibição
            load_p_mw = load_p / 1e6
            load_q_mvar = load_q / 1e6

            print(f"AVISO: Formato antigo de configuração detectado. Convertendo para formato com cargas individualizadas.")
            print(f"Valores de carga na configuração: {load_p_pu:.4f} pu ({load_p_mw:.2f} MW), {load_q_pu:.4f} pu ({load_q_mvar:.2f} MVAR)")

            # Criar estrutura de cargas individualizadas para compatibilidade com o novo formato
            bus_loads = {}
            for bus_id in ["A", "B", "C", "D"]:
                bus_loads[bus_id] = {"load_p": load_p_pu, "load_q": load_q_pu}

            # Atualizar bus_data com a nova estrutura
            bus_data["loads"] = bus_loads

            # Remover os campos antigos
            if "load_p" in bus_data:
                del bus_data["load_p"]
            if "load_q" in bus_data:
                del bus_data["load_q"]

            # Chamar recursivamente esta função com os dados atualizados
            return update_system_config_from_bus_data(config, bus_data)
        else:
            # Compatibilidade retroativa: usar valores padrão
            print("Nenhum valor de carga encontrado na configuração, usando padrões")

            # Valores padrão em pu
            load_p_pu = 0.075  # 0.075 pu (2.25MW/30MVA)
            load_q_pu = 0.0363  # 0.0363 pu (1.089MVAR/30MVA)

            # Converter para valores absolutos
            load_p = load_p_pu * config.s_base  # Converter para W
            load_q = load_q_pu * config.s_base  # Converter para VAr

            # Criar estrutura de cargas individualizadas
            bus_loads = {}
            for bus_id in ["A", "B", "C", "D"]:
                bus_loads[bus_id] = {"load_p": load_p_pu, "load_q": load_q_pu}

            # Atualizar bus_data com a nova estrutura
            bus_data["loads"] = bus_loads

            # Chamar recursivamente esta função com os dados atualizados
            return update_system_config_from_bus_data(config, bus_data)

    # Imprimir a configuração de carga
    print("\nConfiguração de Carga:")
    for bus_id, load in config.loads.items():
        # Calcular valores em pu para exibição
        load_p_pu = load['P'] / config.s_base
        load_q_pu = load['Q'] / config.s_base
        print(f"Bus {bus_id}: {load_p_pu:.4f} pu ({load['P']/1e6:.2f} MW), {load_q_pu:.4f} pu ({load['Q']/1e6:.2f} MVAR)")

    # Atualizar valores base
    config.s_base = bus_data.get("s_base", config.s_base)
    config.v_base = bus_data.get("v_base", config.v_base)
    config.f_base = bus_data.get("f_base", config.f_base)

    # Recalcular valores base derivados após atualização
    config.z_base = (config.v_base ** 2) / config.s_base
    config.i_base = config.s_base / config.v_base

    print(f"\nValores base do sistema:")
    print(f"  Potência base (s_base): {config.s_base/1e6:.2f} MVA")
    print(f"  Tensão base (v_base): {config.v_base/1e3:.2f} kV")
    print(f"  Frequência base (f_base): {config.f_base:.2f} Hz")
    print(f"  Impedância base (z_base): {config.z_base:.4f} Ω")
    print(f"  Corrente base (i_base): {config.i_base:.2f} A")

def validate_generator_configs(generator_configs: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """
    Validar configurações dos geradores e aplicar padrões onde necessário.

    Args:
        generator_configs: Dicionário de configurações dos geradores

    Returns:
        Configurações dos geradores validadas
    """
    for key in REQUIRED_GENERATOR_KEYS:
        # Verificar se a configuração do gerador existe
        if key not in generator_configs:
            log_message(f"Aviso: '{key}' ausente na configuração. Usando padrão.", level="warning")
            generator_configs[key] = DEFAULT_GENERATOR_CONFIGS[key]

        # Verificar se o modo é válido
        if "mode" not in generator_configs[key] or generator_configs[key]["mode"] not in VALID_GENERATOR_MODES:
            default_mode = DEFAULT_GENERATOR_CONFIGS[key]["mode"]
            log_message(f"Aviso: Modo inválido para '{key}'. Usando padrão: {default_mode}", level="warning")
            generator_configs[key]["mode"] = default_mode

    return generator_configs

def create_generators(generator_configs: Dict[str, Dict[str, Any]], config: SystemConfig) -> List[Generator]:
    """
    Criar objetos geradores a partir das configurações.

    Args:
        generator_configs: Dicionário de configurações dos geradores
        config: Objeto SystemConfig

    Returns:
        Lista de objetos Generator
    """
    generators = [
        Generator("A", generator_configs["generator_A"]["mode"], config, generator_configs["generator_A"]),
        Generator("B", generator_configs["generator_B"]["mode"], config, generator_configs["generator_B"]),
        Generator("C", generator_configs["generator_C"]["mode"], config, generator_configs["generator_C"]),
        Generator("D", generator_configs["generator_D"]["mode"], config, generator_configs["generator_D"])
    ]

    # Registrar modos dos geradores
    for gen in generators:
        print(f"Generator {gen.name} configured in mode: {gen.mode}")

    return generators

def display_generator_modes(generators):
    """
    Exibir o modo de operação de cada gerador.

    Args:
        generators: Lista de objetos Generator
    """
    print("\nModos de Operação dos Geradores:")
    print("--------------------------")
    for gen in generators:
        mode_str = "SÍNCRONO" if gen.mode == MODE_SYNCHRONOUS else "DROOP"
        print(f"Gerador {gen.name}: {mode_str}")
    print("--------------------------\n")

def check_plc_connectivity(ip_address, timeout=1):
    """
    Verifica se o PLC está acessível através de ping.

    Args:
        ip_address: Endereço IP do PLC
        timeout: Tempo limite para o ping em segundos

    Returns:
        bool: True se o PLC responder ao ping, False caso contrário
    """
    import subprocess
    import platform

    # Determina o comando de ping com base no sistema operacional
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    timeout_param = '-w' if platform.system().lower() == 'windows' else '-W'

    # Constrói o comando de ping
    command = ['ping', param, '1', timeout_param, str(timeout), ip_address]

    try:
        log_message(f"Verificando conectividade com o PLC em {ip_address}...", level="info")
        # Executa o comando de ping
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # Verifica se o ping foi bem-sucedido
        if result.returncode == 0:
            log_message(f"PLC em {ip_address} está respondendo.", level="info")
            return True
        else:
            log_message(f"PLC em {ip_address} não está respondendo ao ping.", level="warning")
            return False
    except Exception as e:
        log_message(f"Erro ao verificar conectividade com o PLC: {e}", level="error")
        return False

def test_plc_communication(plc_ip="*************", timeout=1):
    """
    Testa a comunicação com o PLC.

    Esta função realiza um teste completo da comunicação com o PLC:
    1. Verifica a conectividade básica usando ping
    2. Tenta estabelecer uma conexão usando a biblioteca pylogix
    3. Tenta ler um valor básico do PLC

    Args:
        plc_ip: Endereço IP do PLC
        timeout: Tempo limite para o teste em segundos

    Returns:
        dict: Dicionário com os resultados do teste
    """
    import time

    log_message(f"Iniciando teste de comunicação com o PLC em {plc_ip}...", level="info")

    results = {
        "ping_success": False,
        "connection_success": False,
        "read_success": False,
        "details": {},
        "overall_status": "Falha"
    }

    # Passo 1: Verificar conectividade básica com ping
    ping_start_time = time.time()
    results["ping_success"] = check_plc_connectivity(plc_ip, timeout)
    ping_time = time.time() - ping_start_time
    results["details"]["ping_time"] = f"{ping_time:.3f} segundos"

    if not results["ping_success"]:
        log_message(f"Teste de comunicação com o PLC falhou no passo 1 (ping).", level="error")
        results["overall_status"] = "Falha no ping"
        return results

    # Passo 2: Tentar estabelecer uma conexão usando pylogix
    try:
        from pylogix import PLC

        log_message(f"Tentando estabelecer conexão com o PLC em {plc_ip}...", level="info")

        conn_start_time = time.time()
        plc = PLC()
        plc.IPAddress = plc_ip

        # Tentar abrir a conexão
        conn_time = time.time() - conn_start_time

        # Tentar estabelecer conexão explicitamente
        ret = plc.Read('@UDT')  # Tenta ler uma tag básica para verificar conexão

        if ret.Status == "Success":
            log_message(f"Conexão estabelecida com o PLC em {plc_ip}.", level="info")
            results["connection_success"] = True
            results["details"]["connection_time"] = f"{conn_time:.3f} segundos"

            # Passo 3: Tentar ler um valor básico do PLC
            try:
                log_message(f"Tentando ler um valor do PLC...", level="info")

                read_start_time = time.time()
                # Tentar ler o status do processador (tag padrão disponível em PLCs Allen-Bradley)
                ret = plc.GetPLCTime()
                read_time = time.time() - read_start_time

                if ret.Status == "Success":
                    log_message(f"Leitura bem-sucedida do PLC: {ret.Value}", level="info")
                    results["read_success"] = True
                    results["details"]["read_time"] = f"{read_time:.3f} segundos"
                    results["details"]["plc_time"] = str(ret.Value)
                    results["overall_status"] = "Sucesso"
                else:
                    log_message(f"Falha ao ler valor do PLC: {ret.Status}", level="warning")
                    results["details"]["read_error"] = ret.Status
                    results["overall_status"] = "Falha na leitura"
            except Exception as e:
                log_message(f"Erro ao ler valor do PLC: {e}", level="error")
                results["details"]["read_error"] = str(e)
                results["overall_status"] = "Erro na leitura"

            # Fechar a conexão
            plc.Close()
        else:
            log_message(f"Falha ao estabelecer conexão com o PLC em {plc_ip}.", level="warning")
            results["details"]["connection_error"] = "Socket não conectado"
            results["overall_status"] = "Falha na conexão"
    except ImportError:
        log_message("Biblioteca pylogix não encontrada. Instale-a com 'pip install pylogix'.", level="error")
        results["details"]["connection_error"] = "Biblioteca pylogix não encontrada"
        results["overall_status"] = "Biblioteca não encontrada"
    except Exception as e:
        log_message(f"Erro ao conectar ao PLC: {e}", level="error")
        results["details"]["connection_error"] = str(e)
        results["overall_status"] = "Erro na conexão"

    # Resumo do teste
    if results["overall_status"] == "Sucesso":
        log_message("Teste de comunicação com o PLC concluído com SUCESSO.", level="info")
    else:
        log_message(f"Teste de comunicação com o PLC concluído com FALHA: {results['overall_status']}", level="warning")

    return results

def initialize_plc_communication(simulation, plc_config):
    """
    Inicializa a comunicação com o PLC.

    Args:
        simulation: Instância da classe Simulation
        plc_config: Configuração do PLC do arquivo config.json

    Returns:
        PLCCommunication: Instância da classe de comunicação com o PLC ou None se desabilitado/erro
    """
    # Verifica se a comunicação com o PLC está habilitada
    if not plc_config.get("enabled", False):
        log_message("Comunicação com o PLC desabilitada na configuração.", level="info")
        return None

    try:
        # Obter configurações do PLC
        plc_ip = plc_config.get("ip", "*************")
        update_interval = plc_config.get("update_interval", 0.1)

        # Verificar se o PLC está respondendo
        if not check_plc_connectivity(plc_ip):
            log_message(f"PLC em {plc_ip} não está respondendo. Comunicação desativada.", level="warning")
            return None

        log_message(f"Iniciando comunicação com o PLC em {plc_ip} (intervalo: {update_interval*1000:.0f}ms)...", level="info")

        plc_comm = setup_plc_communication(
            simulation=simulation,
            plc_ip=plc_ip,
            update_interval=update_interval
        )

        log_message("Comunicação com o PLC iniciada com sucesso.", level="info")
        return plc_comm
    except Exception as e:
        error_message = f"Erro ao inicializar comunicação com o PLC: {e}"
        log_message(error_message, level="error")

        # Registrar o traceback completo
        import traceback
        error_traceback = traceback.format_exc()
        logging.error(error_traceback)

        return None

def parse_arguments():
    """
    Analisa os argumentos de linha de comando.

    Returns:
        argparse.Namespace: Objeto contendo os argumentos de linha de comando
    """
    parser = argparse.ArgumentParser(description="Simulação de Sistema Elétrico de Potência")

    # Adicionar argumentos
    parser.add_argument("--test-plc", action="store_true",
                        help="Testa a comunicação com o PLC e sai")
    parser.add_argument("--plc-ip", type=str, default="*************",
                        help="Endereço IP do PLC para teste (padrão: *************)")
    parser.add_argument("--timeout", type=float, default=1.0,
                        help="Tempo limite para o teste de comunicação em segundos (padrão: 1.0)")

    return parser.parse_args()

def main() -> None:
    """
    Função principal para inicializar e executar a simulação do sistema elétrico de potência.
    """
    # Configurar o registro de logs para redirecionar todas as declarações de impressão para um arquivo de log
    setup_logging()

    # Analisar argumentos de linha de comando
    args = parse_arguments()

    # Verificar se o usuário solicitou o teste de comunicação com o PLC
    if args.test_plc:
        log_message(f"Modo de teste de comunicação com o PLC ativado. IP: {args.plc_ip}, Timeout: {args.timeout}s", level="info")
        results = test_plc_communication(plc_ip=args.plc_ip, timeout=args.timeout)

        # Exibir resultados do teste
        print("\n" + "="*50)
        print("RESULTADOS DO TESTE DE COMUNICAÇÃO COM O PLC")
        print("="*50)
        print(f"Status geral: {results['overall_status']}")
        print(f"Ping: {'Sucesso' if results['ping_success'] else 'Falha'}")
        print(f"Conexão: {'Sucesso' if results['connection_success'] else 'Falha'}")
        print(f"Leitura: {'Sucesso' if results['read_success'] else 'Falha'}")
        print("\nDetalhes:")
        for key, value in results["details"].items():
            print(f"  {key}: {value}")
        print("="*50 + "\n")

        # Sair após o teste
        sys.exit(0)

    log_message("Iniciando simulação do sistema elétrico de potência...")

    # Load configuration
    generator_configs, breaker_status, config, plc_config = load_configuration()

    # Create generators
    generators = create_generators(generator_configs, config)

    # Display generator modes
    display_generator_modes(generators)

    # Create power system
    log_message("Criando sistema elétrico de potência...")
    try:
        system = PowerSystem(config, generators, breaker_status)
        log_message("Sistema elétrico de potência criado.")

        # Imprimir setpoints dos geradores
        print("\nSetpoints dos geradores:")
        for gen in generators:
            print(f"Gerador {gen.name}: P_setpoint = {gen.P_setpoint:.4f} pu")

        # Atribuir barramentos aos geradores
        print("Atribuindo barramentos aos geradores...")
        for gen, bus in zip(generators, system.secondary_buses):
            gen.bus = bus  # Definir a referência de barramento para cada gerador
        print("Barramentos atribuídos aos geradores.")

        # Criar simulação
        log_message("Criando simulação...")
        simulation = Simulation(system)
        log_message("Simulação criada.")

        # Inicializar comunicação com o PLC
        plc_comm = initialize_plc_communication(simulation, plc_config)

        # Criar e executar a interface gráfica
        log_message("Iniciando interface gráfica...")
        app = wx.App(False)
        log_message("wx.App criado")
        try:
            log_message("Criando ElectricalBusGUI...")
            gui = ElectricalBusGUI(None, config.t, generators, system.main_bus, simulation)
            log_message("ElectricalBusGUI criado com sucesso")
        except Exception as e:
            error_message = f"Erro ao criar interface gráfica: {e}"
            logging.error(error_message)
            print(error_message)
            import traceback
            error_traceback = traceback.format_exc()
            logging.error(error_traceback)
            traceback.print_exc()
            sys.exit(1)
        # Don't maximize to keep window smaller for RDP sessions
    except Exception as e:
        error_message = f"Erro ao criar sistema elétrico de potência: {e}"
        logging.error(error_message)
        print(error_message)

        # Registrar o traceback completo
        import traceback
        error_traceback = traceback.format_exc()
        logging.error(error_traceback)

        sys.exit(1)
    log_message("Atualizando índice de tempo...")
    if hasattr(gui, "update_time_index") and callable(gui.update_time_index):
        gui.update_time_index(0)  # Inicializar time_index para 0
    else:
        log_message("Erro: O objeto 'gui' não possui o método 'update_time_index'.", level="error")
    log_message("Mostrando interface gráfica...")
    gui.Show()
    log_message("Iniciando MainLoop...")
    app.MainLoop()
    log_message("Interface gráfica fechada.")

    # Parar a comunicação com o PLC se estiver ativa
    if 'plc_comm' in locals() and plc_comm:
        log_message("Parando comunicação com o PLC...", level="info")
        plc_comm.stop()
        log_message("Comunicação com o PLC encerrada.", level="info")

if __name__ == "__main__":
    main()
