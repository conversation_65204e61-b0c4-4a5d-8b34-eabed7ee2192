"""
Constantes e configuração para a simulação do sistema elétrico de potência.

Este módulo define a classe SystemConfig e várias constantes utilizadas em toda
a simulação.
"""

import json
import os
import numpy as np
from typing import Dict, Any, Optional, List


class SystemConfig:
    """
    Parâmetros de configuração para o sistema elétrico de potência.

    Esta classe contém os valores base e outros parâmetros de configuração para a
    simulação do sistema elétrico de potência.

    Atributos:
        s_base (float): Potência aparente base em VA
        v_base (float): Tensão base em V
        f_base (float): Frequência base em Hz
        z_base (float): Impedância base em ohms (derivada)
        i_base (float): Corrente base em A (derivada)
    """

    def __init__(self, s_base: float = 30e6, v_base: float = 13800.0, f_base: float = 60.0):
        """
        Inicializa a configuração do sistema com valores base.

        Args:
            s_base: Potência aparente base em VA (padrão: 30 MVA)
            v_base: Tensão base em V (padrão: 13.8 kV)
            f_base: Frequência base em Hz (padrão: 60 Hz)
        """
        self.s_base = s_base
        self.v_base = v_base
        self.f_base = f_base

        # Valores base derivados
        self.z_base = (v_base ** 2) / s_base
        self.i_base = s_base / v_base

        # Parâmetros de tempo da simulação
        self.dt = 0.01  # Passo de tempo em segundos
        self.simulation_duration = 15.0  # Duração da simulação em segundos

        # Criar vetor de tempo como um array NumPy explícito
        try:
            # Usar linspace em vez de arange para garantir um número exato de pontos
            num_points = int(self.simulation_duration / self.dt) + 1
            self.t = np.linspace(0, self.simulation_duration, num_points)
            print(f"Vetor de tempo criado com {len(self.t)} pontos de 0 a {self.simulation_duration}s")
        except Exception as e:
            print(f"Erro ao criar vetor de tempo: {e}")
            # Fallback para um valor escalar em caso de erro
            self.t = 0.01  # Usar apenas o passo de tempo como fallback

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SystemConfig':
        """
        Cria uma instância de SystemConfig a partir de um dicionário.

        Args:
            config_dict: Dicionário contendo parâmetros de configuração

        Returns:
            Uma nova instância de SystemConfig
        """
        return cls(
            s_base=config_dict.get('s_base', 30e6),
            v_base=config_dict.get('v_base', 13800.0),
            f_base=config_dict.get('f_base', 60.0)
        )

    @classmethod
    def from_json(cls, json_file: str) -> 'SystemConfig':
        """
        Cria uma instância de SystemConfig a partir de um arquivo JSON.

        Args:
            json_file: Caminho para o arquivo de configuração JSON

        Returns:
            Uma nova instância de SystemConfig

        Raises:
            FileNotFoundError: Se o arquivo JSON não existir
            json.JSONDecodeError: Se o arquivo JSON for inválido
        """
        with open(json_file, 'r') as f:
            config_dict = json.load(f)

        return cls.from_dict(config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a configuração para um dicionário.

        Returns:
            Representação em dicionário da configuração
        """
        return {
            's_base': self.s_base,
            'v_base': self.v_base,
            'f_base': self.f_base
        }

    def to_json(self, json_file: str) -> None:
        """
        Salva a configuração em um arquivo JSON.

        Args:
            json_file: Caminho para o arquivo JSON a ser salvo
        """
        with open(json_file, 'w') as f:
            json.dump(self.to_dict(), f, indent=4)

    def __str__(self) -> str:
        """Retorna uma representação em string da configuração."""
        return (f"SystemConfig(s_base={self.s_base} VA, "
                f"v_base={self.v_base} V, "
                f"f_base={self.f_base} Hz)")
