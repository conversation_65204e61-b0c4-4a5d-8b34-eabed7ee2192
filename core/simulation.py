
# simulation.py
"""
Este módulo define os componentes de simulação do sistema elétrico de potência.

Ele inclui a classe PowerSystem para gerenciar a topologia e a rede do sistema elétrico de potência,
e a classe Simulation para lidar com a simulação passo a passo do sistema.

Este simulador utiliza implementações simplificadas (classes SimpleDataFrame e SimpleColumn)
para fornecer funcionalidade básica de simulação de sistemas elétricos de potência sem
dependências externas complexas.

As classes SimplifiedNetwork, SimpleDataFrame e SimpleColumn fornecem:
- Cálculos básicos de fluxo de potência
- Simulação de dinâmica de geradores
- Interface gráfica para visualização de resultados
- Controle de disjuntores e perturbações do sistema
"""

import os
import sys
import warnings

# Suprime avisos
warnings.filterwarnings('ignore')

print("Usando simulação simplificada do sistema elétrico de potência")

import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from core.bus import MainBus, SecondaryBus
from core.constants import SystemConfig
from core.generator import (
    Generator, MODE_SYNCHRONOUS, MODE_DROOP,
    STATE_DISCONNECTED, STATE_CONNECTED, STATE_SYNCHRONIZED, STATE_LOADED
)
from core.voltage_controller import VoltageController

# Constantes para o sistema elétrico de potência
BUS_IDS = ["A", "B", "C", "D", "E"]
GENERATOR_BREAKER_MAPPING = {'A': 4, 'B': 5, 'C': 6, 'D': 7, 'E': 3}  # CB5-CB8 for A-D, CB4 for E

# Classe de rede simplificada para quando o pandapower não está disponível
class SimplifiedNetwork:
    """Uma classe de rede simplificada que imita a funcionalidade básica do pandapower."""

    def __init__(self, f_hz):
        """Inicializa a rede simplificada."""
        self.f_hz = f_hz
        self.sn_mva = 30.0
        self.buses = []
        self.lines = []
        self.generators = []
        self.ext_grids = []
        self.loads = []  # Adiciona lista de cargas

        # Contêineres de resultados
        self.res_bus = SimpleDataFrame()
        self.res_line = SimpleDataFrame()
        self.res_gen = SimpleDataFrame()
        self.res_ext_grid = SimpleDataFrame()
        self.res_load = SimpleDataFrame()  # Adiciona contêiner de resultados para cargas

        # Adiciona atributos para compatibilidade com pandapower
        self.line = SimpleDataFrame()  # Para compatibilidade com self.net.line
        self.gen = SimpleDataFrame()   # Para compatibilidade com self.net.gen
        self.ext_grid = SimpleDataFrame()  # Para compatibilidade com self.net.ext_grid

    def create_bus(self, name, vn_kv=13.8):
        """Cria um barramento e retorna seu índice."""
        bus = {
            'name': name,
            'vn_kv': vn_kv,
            'in_service': True,
            'vm_pu': 1.0,
            'va_degree': 0.0
        }
        self.buses.append(bus)
        idx = len(self.buses) - 1

        # Adiciona aos resultados
        self.res_bus.add_row({
            'vm_pu': 1.0,
            'va_degree': 0.0,
            'p_mw': 0.0,
            'q_mvar': 0.0
        })

        return idx

    def create_line(self, from_bus, to_bus, name, length_km=0.1, std_type="CUSTOM"):
        """Cria uma linha e retorna seu índice."""
        line = {
            'name': name,
            'from_bus': from_bus,
            'to_bus': to_bus,
            'length_km': length_km,
            'std_type': std_type,
            'in_service': True
        }
        self.lines.append(line)
        idx = len(self.lines) - 1

        # Adiciona aos resultados
        self.res_line.add_row({
            'p_from_mw': 0.0,
            'q_from_mvar': 0.0,
            'p_to_mw': 0.0,
            'q_to_mvar': 0.0,
            'loading_percent': 0.0
        })

        return idx

    def create_generator(self, bus, name, p_mw=0.0, vm_pu=1.0):
        """Cria um gerador e retorna seu índice."""
        generator = {
            'name': name,
            'bus': bus,
            'p_mw': p_mw,
            'vm_pu': vm_pu,
            'in_service': True
        }
        self.generators.append(generator)
        idx = len(self.generators) - 1

        # Adiciona aos resultados
        self.res_gen.add_row({
            'p_mw': p_mw,
            'q_mvar': 0.0
        })

        return idx

    def create_load(self, bus, p_mw, q_mvar, name):
        """Cria uma carga e retorna seu índice."""
        load = {
            'name': name,
            'bus': bus,
            'p_mw': p_mw,
            'q_mvar': q_mvar,
            'in_service': True
        }
        self.loads.append(load)
        idx = len(self.loads) - 1

        # Adiciona aos resultados
        self.res_load.add_row({
            'p_mw': p_mw,
            'q_mvar': q_mvar
        })

        return idx

    def create_ext_grid(self, bus, name, vm_pu=1.0):
        """Cria uma rede externa e retorna seu índice."""
        ext_grid = {
            'name': name,
            'bus': bus,
            'vm_pu': vm_pu,
            'in_service': True
        }
        self.ext_grids.append(ext_grid)
        idx = len(self.ext_grids) - 1

        # Adiciona aos resultados
        self.res_ext_grid.add_row({
            'p_mw': 0.0,
            'q_mvar': 0.0
        })

        return idx

# Classe DataFrame simples para imitar pandas DataFrame
# Esta implementação serve como fallback quando o pandapower (e pandas) não estão disponíveis,
# fornecendo apenas as funcionalidades básicas necessárias para o simulador funcionar
# em modo simplificado.
class SimpleDataFrame:
    """Uma classe simples para imitar a funcionalidade básica do pandas DataFrame."""

    def __init__(self):
        """Inicializa o DataFrame simples."""
        self.data = []
        self.columns = set()
        self.empty = True

    def add_row(self, row_data):
        """Adiciona uma linha ao DataFrame."""
        self.data.append(row_data)
        self.columns.update(row_data.keys())
        self.empty = False

    def __getattr__(self, name):
        """Trata o acesso a atributos para nomes de colunas."""
        if name in self.columns:
            return SimpleColumn([row.get(name, 0.0) for row in self.data])
        raise AttributeError(f"'SimpleDataFrame' object has no attribute '{name}'")

    def isna(self):
        """Imita a função isna() do pandas."""
        return SimpleDataFrame()

    def any(self):
        """Imita a função any() do pandas."""
        return SimpleDataFrame()

    @property
    def at(self):
        """Propriedade que retorna um objeto para acesso estilo pandas .at[row, col]."""
        return SimpleAtAccessor(self)


class SimpleAtAccessor:
    """Classe para simular o acesso .at do pandas."""

    def __init__(self, dataframe):
        self.df = dataframe

    def __getitem__(self, key):
        """Permite acesso usando .at[row, col]."""
        if isinstance(key, tuple) and len(key) == 2:
            row_idx, col_name = key
            if 0 <= row_idx < len(self.df.data):
                return self.df.data[row_idx].get(col_name, None)
            else:
                raise IndexError("Row index out of range")
        else:
            raise TypeError("Key must be a tuple (row, column)")

    def __setitem__(self, key, value):
        """Permite definir valores usando .at[row, col] = value."""
        if isinstance(key, tuple) and len(key) == 2:
            row_idx, col_name = key
            if 0 <= row_idx < len(self.df.data):
                self.df.data[row_idx][col_name] = value
            else:
                raise IndexError("Row index out of range")
        else:
            raise TypeError("Key must be a tuple (row, column)")

# Classe Column simples para imitar pandas Series
# Esta implementação serve como fallback quando o pandapower (e pandas) não estão disponíveis,
# fornecendo apenas as funcionalidades básicas necessárias para o simulador funcionar
# em modo simplificado.
class SimpleColumn:
    """Uma classe simples para imitar a funcionalidade básica do pandas Series."""

    def __init__(self, data):
        """Inicializa a coluna simples."""
        self.data = data

    def __getitem__(self, idx):
        """Obtém item no índice especificado."""
        return self.data[idx]

    def iloc(self, idx):
        """Imita o acessador iloc do pandas."""
        return self.data[idx]

# Parâmetros da barra de barramento
BUS_BAR_PARAMS = {
    "r_ohm_per_km": 0.018,
    "x_ohm_per_km": 0.08,
    "c_nf_per_km": 0.0,
    "max_i_ka": 2.0
}
BUS_BAR_NAME = "CUSTOM_1000MM2_2000A"
LINE_LENGTH_KM = 0.1

# Parâmetros do gerador
GEN_MIN_P_MW = 0.0
GEN_MAX_P_MW = 100.0
GEN_MIN_Q_MVAR = -50.0
GEN_MAX_Q_MVAR = 50.0
DEFAULT_VOLTAGE_PU = 1.0

# Parâmetros do fluxo de potência
MAX_ITERATIONS = 100
TOLERANCE_MVA = 1e-6



class PowerSystem:
    """
    Gerencia a topologia do sistema elétrico de potência e a rede pandapower.

    Esta classe cria e mantém o modelo do sistema elétrico de potência, incluindo barramentos,
    geradores, cargas e linhas. Também lida com cálculos de fluxo de potência e
    atualiza os estados dos componentes.

    Atributos:
        config (SystemConfig): Parâmetros de configuração do sistema
        generators (List[Generator]): Lista de geradores no sistema
        breaker_status (List[bool]): Estado dos disjuntores (True=fechado, False=aberto)
        secondary_buses (List[SecondaryBus]): Barramentos secundários no sistema
        main_bus (MainBus): Barramento principal no sistema
        net (pandapower.pandapowerNet): Modelo de rede do pandapower
        main_bus_idx (int): Índice do barramento principal na rede pandapower
        secondary_buses_idx (List[int]): Índices dos barramentos secundários na rede pandapower
        lines_idx (List[int]): Índices das linhas na rede pandapower
        gen_idx (List[int]): Índices dos geradores na rede pandapower
    """
    def __init__(self, config: SystemConfig, generators: List[Generator], breaker_status: List[bool]):
        """
        Inicializa o sistema elétrico de potência com a configuração fornecida.

        Args:
            config: Parâmetros de configuração do sistema
            generators: Lista de geradores no sistema
            breaker_status: Estado dos disjuntores (True=fechado, False=aberto)

        Raises:
            ValueError: Se breaker_status for inválido ou se o número de geradores não corresponder ao número de barramentos
        """
        try:
            self.config = config
            self.generators = generators
            self.breaker_status = breaker_status

            # Valida o estado dos disjuntores
            self._validate_breaker_status()

            # Cria barramentos
            self._create_buses()

            # Valida a contagem de geradores e barramentos
            if len(self.generators) != len(self.secondary_buses):
                raise ValueError("O número de geradores deve corresponder ao número de barramentos secundários.")

            # Atribui geradores aos barramentos secundários
            self._assign_generators_to_buses()

            # Inicializa a rede pandapower
            self._initialize_pandapower_network()



            # Inicializa o controlador de tensão
            self.voltage_controller = VoltageController(
                config=self.config,
                generators=self.generators,
                main_bus=self.main_bus,
                secondary_buses=self.secondary_buses
            )

        except Exception as e:
            print(f"Erro ao inicializar PowerSystem: {str(e)}")
            raise

    def _validate_breaker_status(self) -> None:
        """
        Valida que breaker_status é uma lista de booleanos.

        Raises:
            ValueError: Se breaker_status não for uma lista de booleanos
        """
        if not isinstance(self.breaker_status, list) or not all(isinstance(b, bool) for b in self.breaker_status):
            raise ValueError("breaker_status deve ser uma lista de booleanos.")

    def _create_buses(self) -> None:
        """Cria barramentos principal e secundários."""
        self.secondary_buses = [SecondaryBus(self.config, bus_id) for bus_id in BUS_IDS]
        self.main_bus = MainBus(self.config)

        # Define valores de carga para cada barramento a partir da configuração
        if hasattr(self.config, 'loads'):
            print("\nConfig possui atributo loads. Cargas disponíveis:")
            for bus_id, load in self.config.loads.items():
                print(f"  Carga de configuração para Barramento {bus_id}: {load['P']/1e6:.2f} MW, {load['Q']/1e6:.2f} MVAR")

            for i, bus_id in enumerate(BUS_IDS):
                if bus_id in self.config.loads:
                    # Define os valores de carga
                    self.secondary_buses[i].load_p = self.config.loads[bus_id]['P']
                    self.secondary_buses[i].load_q = self.config.loads[bus_id]['Q']
                    print(f"Definida carga para Barramento {bus_id}: {self.secondary_buses[i].load_p/1e6:.2f} MW, {self.secondary_buses[i].load_q/1e6:.2f} MVAR")

                    # Verificação de sanidade - garante que a carga não é zero
                    if self.secondary_buses[i].load_p < 1.0:  # Menos de 1 watt
                        print(f"Aviso: Carga do Barramento {bus_id} é muito pequena ou zero: {self.secondary_buses[i].load_p} W")
                        print(f"  Isso pode causar problemas com cálculos de potência.")
                else:
                    print(f"Aviso: Nenhuma configuração de carga encontrada para o Barramento {bus_id}")
                    # Define valores de carga padrão
                    self.secondary_buses[i].load_p = 2.25e6  # 2.25 MW
                    self.secondary_buses[i].load_q = 1.089e6  # 1.089 MVAR
                    print(f"  Usando carga padrão: {self.secondary_buses[i].load_p/1e6:.2f} MW, {self.secondary_buses[i].load_q/1e6:.2f} MVAR")
        else:
            print("Aviso: Nenhum atributo loads na configuração. Definindo cargas padrão.")
            # Define valores de carga padrão para todos os barramentos
            for i, bus in enumerate(self.secondary_buses):
                bus.load_p = 2.25e6  # 2.25 MW
                bus.load_q = 1.089e6  # 1.089 MVAR
                print(f"  Barramento {BUS_IDS[i]}: Usando carga padrão: {bus.load_p/1e6:.2f} MW, {bus.load_q/1e6:.2f} MVAR")

        # Debug: Imprime todas as cargas dos barramentos após a definição
        print("\nCargas finais dos barramentos:")
        for i, bus in enumerate(self.secondary_buses):
            print(f"  Barramento {BUS_IDS[i]}: load_p = {bus.load_p/1e6:.2f} MW, load_q = {bus.load_q/1e6:.2f} MVAR")

    def _assign_generators_to_buses(self) -> None:
        """Atribui geradores aos barramentos secundários."""
        # Primeiro, atribui cada gerador ao seu barramento correspondente
        for gen, bus in zip(self.generators, self.secondary_buses):
            bus.add_generator(gen)

        # Agora, para cada barramento, adiciona todos os geradores do sistema
        # para que possam ser encontrados na dinâmica de frequência
        for bus in self.secondary_buses:
            for gen in self.generators:
                # Adiciona apenas se ainda não estiver na lista
                if gen not in bus.generators:
                    bus.generators.append(gen)

    def _initialize_pandapower_network(self) -> None:
        """Inicializa a rede do sistema elétrico de potência."""
        try:
            # Cria uma rede simplificada
            print("Criando rede simplificada")
            self.net = SimplifiedNetwork(self.config.f_base)

            # Cria barramento principal
            self.main_bus_idx = self.net.create_bus(name="Main Bus")

            # Cria barramentos secundários
            self.secondary_buses_idx = []
            for i, bus_id in enumerate(BUS_IDS):
                bus_idx = self.net.create_bus(name=f"Sec Bus {bus_id}")
                self.secondary_buses_idx.append(bus_idx)

                # Adiciona carga ao barramento
                bus = self.secondary_buses[i]
                if hasattr(bus, 'load_p') and bus.load_p > 0:
                    load_p_mw = bus.load_p / 1e6  # Converte W para MW
                    load_q_mvar = bus.load_q / 1e6  # Converte VAr para MVAr
                    self.net.create_load(
                        bus=bus_idx,
                        p_mw=load_p_mw,
                        q_mvar=load_q_mvar,
                        name=f"Load {bus_id}"
                    )
                    print(f"Adicionada carga ao Barramento {bus_id}: {load_p_mw:.2f} MW, {load_q_mvar:.2f} MVAr")

            # Cria linhas
            self.lines_idx = []
            for i, bus_idx in enumerate(self.secondary_buses_idx):
                line_idx = self.net.create_line(from_bus=self.main_bus_idx, to_bus=bus_idx, name=f"Line {BUS_IDS[i]}")
                self.lines_idx.append(line_idx)

            # Cria geradores
            self.gen_idx = []
            for i, gen in enumerate(self.generators):
                gen_idx = self.net.create_generator(bus=self.secondary_buses_idx[i], name=f"Generator {gen.name}")
                self.gen_idx.append(gen_idx)

                return

            # Se pandapower estiver disponível, use-o
            # Cria rede vazia com uma abordagem mais robusta
            try:
                # Primeiro tenta com a abordagem padrão
                self.net = pp.create_empty_network()

                # Então define os parâmetros manualmente
                self.net.f_hz = float(self.config.f_base)
                self.net.sn_mva = 30.0

                print("Rede vazia criada com sucesso")
            except Exception as e:
                print(f"Erro ao criar rede vazia: {e}")
                raise  # Re-lança a exceção para interromper a execução

            # Cria barramento principal (barramento de referência)
            self.main_bus_idx = pp.create_bus(
                self.net,
                vn_kv=13.8,
                name="Main Bus"
            )

            # Cria barramentos secundários
            self.secondary_buses_idx = []
            for i, bus in enumerate(self.secondary_buses):
                bus_idx = pp.create_bus(
                    self.net,
                    vn_kv=13.8,
                    name=f"Sec Bus {BUS_IDS[i]}"
                )
                self.secondary_buses_idx.append(bus_idx)

                # Adiciona carga ao barramento
                if hasattr(bus, 'load_p') and bus.load_p > 0:
                    load_p_mw = bus.load_p / 1e6  # Converte W para MW
                    load_q_mvar = bus.load_q / 1e6  # Converte VAr para MVAr
                    pp.create_load(
                        self.net,
                        bus=bus_idx,
                        p_mw=load_p_mw,
                        q_mvar=load_q_mvar,
                        name=f"Load {BUS_IDS[i]}"
                    )
                    print(f"Adicionada carga ao Barramento {BUS_IDS[i]}: {load_p_mw:.2f} MW, {load_q_mvar:.2f} MVAr")

            # Cria linhas conectando o barramento principal aos barramentos secundários
            self.lines_idx = []
            for i, bus_idx in enumerate(self.secondary_buses_idx):
                # Cria tipo de linha padrão se não existir
                if BUS_BAR_NAME not in self.net.std_types["line"]:
                    pp.create_std_type(
                        self.net,
                        {
                            "r_ohm_per_km": BUS_BAR_PARAMS["r_ohm_per_km"],
                            "x_ohm_per_km": BUS_BAR_PARAMS["x_ohm_per_km"],
                            "c_nf_per_km": BUS_BAR_PARAMS["c_nf_per_km"],
                            "max_i_ka": BUS_BAR_PARAMS["max_i_ka"]
                        },
                        name=BUS_BAR_NAME,
                        element="line"
                    )

                # Cria linha
                line_idx = pp.create_line(
                    self.net,
                    from_bus=self.main_bus_idx,
                    to_bus=bus_idx,
                    length_km=LINE_LENGTH_KM,
                    std_type=BUS_BAR_NAME,
                    name=f"Line {BUS_IDS[i]}"
                )
                self.lines_idx.append(line_idx)

            # Cria geradores
            self.gen_idx = []
            for i, gen in enumerate(self.generators):
                # Converte parâmetros do gerador para o formato pandapower
                p_mw = gen.P_setpoint * 30.0  # Converte de pu para MW

                # Cria gerador
                if gen.mode == MODE_SYNCHRONOUS:
                    # Gerador de referência (slack)
                    gen_idx = pp.create_ext_grid(
                        self.net,
                        bus=self.secondary_buses_idx[i],
                        vm_pu=DEFAULT_VOLTAGE_PU,
                        name=f"Generator {gen.name}"
                    )
                else:
                    # Gerador PV
                    gen_idx = pp.create_gen(
                        self.net,
                        bus=self.secondary_buses_idx[i],
                        p_mw=p_mw,
                        vm_pu=DEFAULT_VOLTAGE_PU,
                        min_p_mw=GEN_MIN_P_MW,
                        max_p_mw=GEN_MAX_P_MW,
                        min_q_mvar=GEN_MIN_Q_MVAR,
                        max_q_mvar=GEN_MAX_Q_MVAR,
                        name=f"Generator {gen.name}"
                    )
                self.gen_idx.append(gen_idx)

                print(f"Criando gerador pandapower {gen.name} com p_mw = {p_mw:.4f} MW, "
                      f"slack = {gen.mode == MODE_SYNCHRONOUS}, mode = {gen.mode}")

            # Executa fluxo de potência inicial
            if not self.run_power_flow():
                print("Aviso: Fluxo de potência inicial não convergiu")

        except Exception as e:
            print(f"Erro ao inicializar rede pandapower: {str(e)}")
            raise

    def update_network(self) -> None:
        """Atualiza a rede pandapower com base no estado dos disjuntores."""
        try:
            # Atualiza o estado das linhas com base no estado dos disjuntores
            for i, line_idx in enumerate(self.lines_idx):
                breaker_idx = i  # Disjuntores 0-3 controlam linhas
                self.net.line.at[line_idx, "in_service"] = self.breaker_status[breaker_idx]

            # Atualiza o estado dos geradores com base no estado dos disjuntores
            for i, gen in enumerate(self.generators):
                breaker_idx = GENERATOR_BREAKER_MAPPING[gen.name]
                is_connected = self.breaker_status[breaker_idx]
                gen.is_connected = is_connected

                if gen.mode == MODE_SYNCHRONOUS:
                    # Atualiza ext_grid
                    self.net.ext_grid.at[self.gen_idx[i], "in_service"] = is_connected
                else:
                    # Atualiza gen
                    self.net.gen.at[self.gen_idx[i], "in_service"] = is_connected

                # Atualiza setpoint do gerador se conectado
                if is_connected:
                    if gen.mode != MODE_SYNCHRONOUS:
                        p_mw = gen.P_setpoint * 30.0  # Converte de pu para MW
                        self.net.gen.at[self.gen_idx[i], "p_mw"] = p_mw

            # Imprime informações de depuração
            if any(not status for status in self.breaker_status):
                print("\nEstado dos disjuntores:", self.breaker_status)
                print("Estado das linhas:", self.net.line["in_service"].tolist())
                if len(self.net.gen) > 0:
                    print("Estado dos geradores:", self.net.gen["in_service"].tolist())
                if len(self.net.ext_grid) > 0:
                    print("Estado da rede externa:", self.net.ext_grid["in_service"].tolist())

        except Exception as e:
            print(f"Erro ao atualizar rede: {str(e)}")
            raise

    def run_power_flow(self) -> bool:
        """
        Executa a análise de fluxo de potência.

        Returns:
            bool: True se o fluxo de potência convergiu com sucesso, False caso contrário.
        """
        try:
            if not USE_PANDAPOWER:
                # Cálculo simplificado de fluxo de potência
                print("Executando cálculo simplificado de fluxo de potência")

                # Define a saída de potência do gerador
                for i, gen in enumerate(self.generators):
                    if i < len(self.gen_idx):
                        gen_idx = self.gen_idx[i]
                        self.net.generators[gen_idx]['p_mw'] = gen.P_elec[-1] * self.config.s_base

                # Define tensões dos barramentos (cálculo simplificado)
                for i, bus_idx in enumerate(self.secondary_buses_idx):
                    if i < len(self.generators):
                        gen = self.generators[i]
                        self.net.res_bus.data[bus_idx]['vm_pu'] = gen.V_t[-1]

                # Define fluxos nas linhas (cálculo simplificado)
                for i, line_idx in enumerate(self.lines_idx):
                    if i < len(self.generators):
                        gen = self.generators[i]
                        self.net.res_line.data[line_idx]['p_from_mw'] = gen.P_elec[-1] * self.config.s_base
                        self.net.res_line.data[line_idx]['p_to_mw'] = -gen.P_elec[-1] * self.config.s_base

                return True

            # Se pandapower estiver disponível, use-o
            # Limpa resultados anteriores
            if hasattr(self.net, 'res_bus'):
                self.net.res_bus = None
            if hasattr(self.net, 'res_line'):
                self.net.res_line = None
            if hasattr(self.net, 'res_gen'):
                self.net.res_gen = None
            if hasattr(self.net, 'res_ext_grid'):
                self.net.res_ext_grid = None

            # Usa a configuração de pandapower_config
            from config.pandapower_config import SOLVER_CONFIG

            # Tenta executar fluxo de potência com nossas configurações seguras
            pp.runpp(self.net, **SOLVER_CONFIG)

            # Valida resultados
            if not self._validate_power_flow_results():
                print("Resultados do fluxo de potência são inválidos")
                return False

            # Imprime informações de depuração sobre os resultados
            if hasattr(self.net, 'res_bus') and not self.net.res_bus.empty:
                print("Fluxo de potência convergiu. Tensões dos barramentos (pu):")
                for i, vm in enumerate(self.net.res_bus.vm_pu):
                    bus_name = self.net.bus.name[i] if 'name' in self.net.bus.columns else f"Bus {i}"
                    print(f"  {bus_name}: {vm:.4f} pu")

            return True

        except Exception as e:
            print(f"Erro durante o cálculo do fluxo de potência: {str(e)}")
            return False

    def _validate_power_flow_results(self) -> bool:
        """
        Valida os resultados do fluxo de potência para garantir que são válidos e não contêm valores NaN.

        Returns:
            bool: True se os resultados são válidos, False caso contrário.
        """
        try:
            if not USE_PANDAPOWER:
                # Para rede simplificada, assumimos que os resultados são válidos
                return True

            # Para rede pandapower, realiza validação
            # Verifica se os resultados existem
            if not hasattr(self.net, 'res_bus') or not hasattr(self.net, 'res_line'):
                print("Resultados do fluxo de potência ausentes")
                return False

            # Verifica valores NaN nos resultados dos barramentos
            if self.net.res_bus.isna().any().any():
                print("Valores NaN encontrados nos resultados dos barramentos")
                return False

            # Verifica valores NaN nos resultados das linhas
            if self.net.res_line.isna().any().any():
                print("Valores NaN encontrados nos resultados das linhas")
                return False

            # Verifica se as magnitudes de tensão são razoáveis (0.8 a 1.2 pu)
            if not ((self.net.res_bus.vm_pu >= 0.8) & (self.net.res_bus.vm_pu <= 1.2)).all():
                print("Magnitudes de tensão fora da faixa razoável")
                return False

            # Verifica se os carregamentos das linhas são razoáveis (menos de 200%)
            if (self.net.res_line.loading_percent > 200).any():
                print("Sobrecarga de linha detectada")
                return False

            return True

        except Exception as e:
            print(f"Erro ao validar resultados do fluxo de potência: {str(e)}")
            return False

    def update_buses(self, i: int) -> None:
        """
        Atualiza todos os barramentos no sistema.

        Args:
            i: Índice de tempo atual
        """
        self.main_bus.update(i, self.breaker_status)
        for bus in self.secondary_buses:
            bus.update(i, self.breaker_status)

    def update_generator_states(self, i: int) -> bool:
        """
        Atualiza os estados dos geradores com base no estado dos disjuntores e nos resultados do fluxo de potência.

        Este método atualiza os valores de potência elétrica dos geradores a partir dos
        resultados do fluxo de potência e atualiza o estado dos geradores.

        Args:
            i: Índice de tempo atual

        Returns:
            bool: True se os estados dos geradores foram atualizados com sucesso, False caso contrário.
        """
        try:
            # Atualiza cada gerador
            for gen_idx, gen in enumerate(self.generators):
                breaker_idx = GENERATOR_BREAKER_MAPPING[gen.name]
                is_connected = self.breaker_status[breaker_idx]

                # Atualiza o estado do gerador com base no estado do disjuntor
                gen.update_generator_state(i, is_connected)

                if not is_connected:
                    # Se o gerador está desconectado, não precisamos atualizar mais nada
                    continue

                try:
                    # Obtém índice do barramento para este gerador
                    bus_idx = self.secondary_buses_idx[gen_idx]

                    # Obtém resultados do fluxo de potência para este barramento
                    if gen.mode == MODE_SYNCHRONOUS:
                        # Para gerador de referência, obtém valores dos resultados de ext_grid
                        P_mw = float(self.net.res_ext_grid.p_mw.iloc[gen_idx])
                        Q_mvar = float(self.net.res_ext_grid.q_mvar.iloc[gen_idx])
                    else:
                        # Para geradores PV, obtém valores dos resultados de gen
                        P_mw = float(self.net.res_gen.p_mw.iloc[gen_idx])
                        Q_mvar = float(self.net.res_gen.q_mvar.iloc[gen_idx])

                    # Obtém magnitude de tensão
                    V_pu = float(self.net.res_bus.vm_pu.iloc[bus_idx])

                    # Converte potência de MW/MVAr para pu
                    P_pu = P_mw / 30.0  # Base de 30 MVA
                    Q_pu = Q_mvar / 30.0

                    # Atualiza estado do gerador
                    gen.P_elec[i] = P_pu
                    gen.Q_elec[i] = Q_pu
                    gen.V_t[i] = V_pu

                except Exception as e:
                    print(f"Erro ao atualizar gerador {gen.name} no passo {i}: {str(e)}")
                    # Usa valores de fallback
                    if i > 0:
                        gen.P_elec[i] = gen.P_elec[i-1]
                        gen.Q_elec[i] = gen.Q_elec[i-1]
                        gen.V_t[i] = gen.V_t[i-1]
                    else:
                        gen.P_elec[i] = gen.P_setpoint
                        gen.Q_elec[i] = 0.0
                        gen.V_t[i] = 1.0

            return True

        except Exception as e:
            print(f"Erro em update_generator_states no passo {i}: {str(e)}")
            return False

class Simulation:
    """
    Manages the time-stepping simulation of the power system.

    This class handles the time-domain simulation of the power system, including
    generator dynamics, power flow calculations, and data collection for visualization.

    Attributes:
        system (PowerSystem): Power system being simulated
        config (SystemConfig): System configuration parameters
        t (numpy.ndarray): Time vector for simulation
        current_time_index (int): Current time step index
        gen_data (List[Dict]): Generator data for GUI display
        secondary_bus_powers (List[numpy.ndarray]): Power values for secondary buses
        secondary_bus_frequencies (List[numpy.ndarray]): Frequency values for secondary buses
        secondary_bus_voltages (List[numpy.ndarray]): Voltage values for secondary buses
        ui_update_needed (bool): Flag indicating if UI update is needed
        last_ui_update_index (int): Last time index when UI was updated
    """
    def __init__(self, system: PowerSystem):
        """
        Initialize the simulation with the given power system.

        Args:
            system: Power system to simulate
        """
        self.system = system
        self.config = system.config
        self.t = self.config.t
        self.current_time_index = 0

        # UI update flags
        self.ui_update_needed = True  # Initial update is needed
        self.last_ui_update_index = -1  # No updates yet
        self.ui_update_frequency = 50  # Update UI every 50 steps by default

        # Initialize generator arrays for time-domain simulation
        self._initialize_generator_arrays()

        # Initialize data arrays for visualization
        self._initialize_data_arrays()

    def _initialize_generator_arrays(self) -> None:
        """Initialize generator arrays for time-domain simulation."""
        # Get the length of the time vector
        if isinstance(self.t, np.ndarray):
            time_length = len(self.t)
            print(f"Usando vetor de tempo com {time_length} pontos")
        else:
            # Se t for um escalar, calcular o número de pontos
            time_length = int(self.config.simulation_duration / self.config.dt) + 1
            print(f"Usando valor escalar de tempo dt={self.t}, calculando {time_length} pontos")

        # Initialize arrays for each generator
        for gen in self.system.generators:
            gen.initialize_arrays(time_length)
            print(f"Initialized arrays for generator {gen.name} with length {time_length}")

    def _initialize_data_arrays(self) -> None:
        """Initialize arrays for storing simulation data."""
        # Determine the length of the time vector
        if isinstance(self.t, np.ndarray):
            time_length = len(self.t)
        else:
            # Se t for um escalar, calcular o número de pontos
            time_length = int(self.config.simulation_duration / self.config.dt) + 1

        # Create empty arrays with the correct length
        empty_array = np.zeros(time_length)

        # Generator data for real-time display
        self.gen_data = []
        for gen in self.system.generators:
            gen_data = {
                "P_elec": np.full(time_length, gen.P_elec[0]),  # Electrical power output
                "P_mech": np.full(time_length, gen.P_mech[0]),  # Mechanical power input
                "frequency": np.full(time_length, self.config.f_base),  # Frequency in Hz
                "V_t": np.full(time_length, gen.V_t[0])       # Terminal voltage in pu
            }
            self.gen_data.append(gen_data)

        # Debug: Print bus loads before initializing arrays
        print("\nBus loads during data array initialization:")
        for i, bus in enumerate(self.system.secondary_buses):
            print(f"  Bus {chr(65 + i)}: load_p = {bus.load_p/1e6:.2f} MW, load_q = {bus.load_q/1e6:.2f} MVAR")

        # Bus data for plotting
        self.secondary_bus_powers = []
        self.secondary_bus_frequencies = []
        self.secondary_bus_voltages = []
        self.secondary_bus_power_budgets = []

        for bus in self.system.secondary_buses:
            # Create arrays with the correct length
            self.secondary_bus_powers.append(np.full(time_length, bus.load_p / 1e6))
            self.secondary_bus_frequencies.append(np.full(time_length, self.config.f_base))
            self.secondary_bus_voltages.append(np.full(time_length, self.config.v_base))

            # Power budget data (generation - load)
            self.secondary_bus_power_budgets.append(np.zeros(time_length))

    def print_generator_status(self):
        """Print the current status of all generators."""
        # Calculate current time based on time index
        if isinstance(self.t, np.ndarray) and self.current_time_index < len(self.t):
            current_time = self.t[self.current_time_index]
        else:
            # If t is a scalar or index is out of bounds, calculate time from dt
            current_time = self.current_time_index * self.config.dt

        print(f"\nStatus do Sistema em t = {current_time:.2f}s:")
        print("-" * 50)

        # Informações do barramento principal e controlador de tensão
        try:
            main_bus_voltage = self.system.main_bus.voltage
            v_setpoint = self.system.voltage_controller.v_setpoint
            v_error = v_setpoint - main_bus_voltage

            print(f"Barramento Principal:")
            print(f"  Tensão: {main_bus_voltage:.4f} pu")
            print(f"  Setpoint: {v_setpoint:.4f} pu")
            print(f"  Erro: {v_error:.4f} pu")
            print(f"  Modo de Controle: {'Compartilhamento Q' if self.system.voltage_controller.q_sharing_enabled else 'Droop'}")
            print("-" * 50)
        except Exception as e:
            print(f"Erro ao obter informações do barramento principal: {e}")
            print("-" * 50)

        # Informações dos geradores
        for idx, gen in enumerate(self.system.generators):
            # Get connection status from the system's breaker status
            # Assuming breakers 4-7 correspond to generators A-D
            connected = self.system.breaker_status[idx + 4]
            connected_str = "CONECTADO" if connected else "DESCONECTADO"
            mode = "SÍNCRONO" if gen.mode == "synchronous" else "DROOP"

            # Get generator state
            state_str = gen.state.upper()

            print(f"Gerador {gen.name}: {connected_str}, Modo: {mode}, Estado: {state_str}")
            print(f"  P_elec: {gen.P_elec[self.current_time_index]:.4f} pu")
            print(f"  P_mech: {gen.P_mech[self.current_time_index]:.4f} pu")
            print(f"  Q_elec: {gen.Q_elec[self.current_time_index]:.4f} pu")
            print(f"  Frequência: {gen.omega[self.current_time_index]:.6f} pu ({gen.omega[self.current_time_index]*60:.2f} Hz)")
            print(f"  Tensão: {gen.V_t[self.current_time_index]:.4f} pu")
            print(f"  Setpoint de Tensão: {gen.V_setpoint:.4f} pu")
            print("-" * 50)

    def run_step(self) -> bool:
        """
        Run a single simulation step.

        Returns:
            bool: True if the step was successful, False otherwise.
        """
        try:
            # Get current time index
            i = self.current_time_index

            # Skip if we've reached the end of the simulation
            if isinstance(self.t, np.ndarray) and i >= len(self.t):
                print("Simulation complete (reached end of time array).")
                return False
            elif i * self.config.dt > self.config.simulation_duration:
                print(f"Simulation complete (reached duration {self.config.simulation_duration}s).")
                return False

            # Print progress message occasionally
            if i % 100 == 0:
                # Calculate current time based on time index
                if isinstance(self.t, np.ndarray) and i < len(self.t):
                    current_time = self.t[i]
                else:
                    # If t is a scalar or index is out of bounds, calculate time from dt
                    current_time = i * self.config.dt
                print(f"\nSimulating time step {i} (t = {current_time:.2f}s)")

            # Force time index to advance even if there's an error
            # This ensures the simulation doesn't get stuck
            self.current_time_index += 1

            # Apply generator dynamics to update frequency and power
            for gen in self.system.generators:
                try:
                    # Check if generator is connected
                    breaker_idx = GENERATOR_BREAKER_MAPPING[gen.name]
                    is_connected = self.system.breaker_status[breaker_idx]

                    # Atualiza o estado do gerador com base no estado do disjuntor
                    gen.update_generator_state(i, is_connected)

                    if not is_connected:
                        # Para geradores desconectados, não precisamos fazer mais nada
                        # O método update_generator_state já aplicou as ações necessárias
                        continue

                    # Para geradores conectados, aplicar dinâmicas normais com base no estado
                    if gen.state == STATE_CONNECTED or gen.state == STATE_SYNCHRONIZED or gen.state == STATE_LOADED:
                        # Calcula erro de potência
                        gen.power_error[i] = gen.P_setpoint - gen.P_elec[i]

                        # Aplica controle do governador
                        gen._apply_governor_control(i)

                        # Aplica dinâmica da turbina
                        gen._apply_turbine_dynamics(i)

                        # Calcula dinâmica de frequência
                        gen._calculate_frequency_dynamics(i)

                        # Aplica passo de tempo para atualizar valores
                        dt = 0.01  # Passo de tempo em segundos
                        gen.step(i, dt)

                        # Adiciona perturbações aleatórias para criar variações de frequência
                        # Aplica a todos os geradores para tornar a dinâmica de frequência mais visível
                        if i > 100 and i % 100 == 0:  # Adiciona perturbação a cada 100 passos após estabilização inicial
                            # Perturbação aleatória entre -0.15 e 0.15 pu (magnitude aumentada)
                            disturbance = (np.random.random() - 0.5) * 0.3
                            gen.P_elec[i] += disturbance
                            print(f"Adicionando perturbação de {disturbance:.4f} pu ao gerador {gen.name} no passo {i}")

                            # Também adiciona perturbações maiores ocasionais aos geradores síncronos
                            if gen.mode == MODE_SYNCHRONOUS and i % 500 == 0 and i > 500:
                                # Perturbação maior para geradores síncronos
                                big_disturbance = (np.random.random() - 0.5) * 0.5
                                gen.P_elec[i] += big_disturbance
                                print(f"Adicionando perturbação GRANDE de {big_disturbance:.4f} pu ao gerador síncrono {gen.name} no passo {i}")
                except Exception as e:
                    print(f"Erro ao atualizar dinâmica do gerador {gen.name}: {e}")
                    # Fallback para abordagem simples se o cálculo da dinâmica falhar
                    if i > 0:
                        # Mantém valores anteriores para evitar mudanças abruptas
                        gen.P_elec[i] = gen.P_elec[i-1]
                        gen.Q_elec[i] = gen.Q_elec[i-1]
                        gen.V_t[i] = gen.V_t[i-1]
                        gen.omega[i] = gen.omega[i-1]
                        gen.P_mech[i] = gen.P_mech[i-1]
                    else:
                        # Inicializa com valores padrão
                        gen.P_elec[i] = gen.P_setpoint
                        gen.Q_elec[i] = 0.0
                        gen.V_t[i] = 1.0
                        gen.omega[i] = 1.0
                        gen.P_mech[i] = gen.P_setpoint


            # Atualiza o controle de tensão do barramento comum
            try:
                # Aplica o controle de tensão do barramento comum
                dt = 0.01  # Passo de tempo em segundos
                self.system.voltage_controller.update(i, dt)
            except Exception as e:
                print(f"Erro ao atualizar controle de tensão: {e}")

            # Collect data for visualization
            self._collect_data_for_visualization(i)

            # Print generator status occasionally
            if i % 100 == 0:
                self.print_generator_status()

            return True

        except Exception as e:
            print(f"Erro durante o passo de simulação {self.current_time_index}: {str(e)}")
            # Já incrementamos o índice de tempo no início do método
            # então não precisamos fazer isso novamente aqui
            return False

    def _collect_data_for_visualization(self, i: int) -> None:
        """
        Collect data for visualization.

        Args:
            i: Current time index
        """
        # Collect generator data
        self._collect_generator_data(i)

        # Collect bus data
        self._collect_bus_data(i)

    def _collect_generator_data(self, i: int) -> None:
        """
        Collect real-time data from generators for display.

        Args:
            i: Current time index
        """
        if i < len(self.t):
            # Update generator data arrays with the latest values
            for idx, gen in enumerate(self.system.generators):
                # No need to get data from generator, we'll calculate it directly

                # Calculate current directly here to ensure it's updated
                voltage_pu = gen.V_t[i]
                power_pu = gen.P_elec[i]

                # Calculate current in amperes
                if voltage_pu > 0:
                    current = (power_pu * self.config.s_base) / (voltage_pu * self.config.v_base)
                else:
                    current = 0.0

                # Update the data arrays at the current time index
                self.gen_data[idx]["P_elec"][i] = gen.P_elec[i]
                self.gen_data[idx]["P_mech"][i] = gen.P_mech[i]
                self.gen_data[idx]["frequency"][i] = gen.omega[i] * self.config.f_base  # Convert to Hz
                self.gen_data[idx]["V_t"][i] = gen.V_t[i]  # Store terminal voltage

                # Log generator data with reduced verbosity - only every 100 steps
                if i % 100 == 0:  # Reduced from every 10 steps to every 100 steps
                    print(f"Dados do Gerador {chr(65 + idx)} no passo {i}: "
                          f"P_elec = {self.gen_data[idx]['P_elec'][i]:.4f} pu, "
                          f"P_mech = {self.gen_data[idx]['P_mech'][i]:.4f} pu, "
                          f"f = {self.gen_data[idx]['frequency'][i]:.2f} Hz")

    def _collect_bus_data(self, i: int) -> None:
        """
        Collect data from buses for plotting.

        Args:
            i: Current time index
        """
        if i < len(self.t):
            for idx, bus in enumerate(self.system.secondary_buses):
                gen = self.system.generators[idx]
                gen_breaker_idx = idx + 4  # CB5-CB8
                bus_breaker_idx = idx      # CB1-CB4

                # Voltage in V - handle both cases:
                # 1. Bus connected to main bus through breaker
                # 2. Bus powered by local generator
                if self.system.breaker_status[bus_breaker_idx]:
                    # Bus is connected to main bus, use pandapower result
                    voltage_pu = self.system.net.res_bus.at[self.system.secondary_buses_idx[idx], "vm_pu"]
                elif self.system.breaker_status[gen_breaker_idx]:
                    # Bus is isolated but generator is connected, use generator voltage
                    voltage_pu = gen.V_t[i]
                else:
                    # Bus is completely isolated, voltage is 0
                    voltage_pu = 0.0

                # Convert from per-unit to volts
                # Store in volts for internal use (will be converted to kV for display)
                voltage_v = voltage_pu * self.config.v_base
                self.secondary_bus_voltages[idx][i] = voltage_v  # voltage_v

                # Frequency in Hz - use the generator's frequency
                # Convert from per-unit (omega) to Hz
                self.secondary_bus_frequencies[idx][i] = gen.omega[i] * self.config.f_base  # frequency_hz

                # Use fixed load values directly from the bus
                # This represents a constant power load model, which is more appropriate
                # for representing aggregate loads in a power system

                # Get the load in MW
                load_mw = bus.load_p / 1e6

                # If bus is energized (connected to main bus or generator), use the configured load
                # Otherwise, the load is zero (no power consumption when de-energized)
                if voltage_pu > 0 and (self.system.breaker_status[bus_breaker_idx] or self.system.breaker_status[gen_breaker_idx]):
                    # Use the configured load value
                    actual_power_mw = load_mw

                    # Ensure the load is not zero (this is a sanity check)
                    if actual_power_mw < 0.01 and bus.load_p > 0:
                        print(f"Warning: Bus {chr(65 + idx)} load is very small: {actual_power_mw:.4f} MW")
                        print(f"  Configured load: {bus.load_p/1e6:.4f} MW")
                else:
                    # If bus is not energized, power is zero
                    actual_power_mw = 0.0

                # Store the fixed load power
                self.secondary_bus_powers[idx][i] = actual_power_mw

                # Calculate and store power budget (generation - load)
                if idx < len(self.system.generators):
                    gen = self.system.generators[idx]
                    gen_power_mw = gen.P_elec[i] * (self.config.s_base / 1e6)  # Convert to MW
                    power_budget_mw = gen_power_mw - actual_power_mw
                    self.secondary_bus_power_budgets[idx][i] = power_budget_mw

                # Log all data for debugging - only every 100 steps
                if i % 100 == 0:
                    print(f"Dados do Barramento {chr(65 + idx)} no passo {i}:")
                    print(f"  Potência de Carga: {self.secondary_bus_powers[idx][i]:.2f} MW (configurada: {bus.load_p/1e6:.2f} MW)")
                    print(f"  Potência do Gerador: {gen.P_elec[i] * (self.config.s_base / 1e6):.2f} MW")
                    print(f"  Frequência: {self.secondary_bus_frequencies[idx][i]:.2f} Hz")
                    print(f"  Tensão: {voltage_v/1000:.2f} kV (PU: {voltage_pu:.4f})")
                    print(f"  Disjuntor do barramento (CB{idx+1}): {'fechado' if self.system.breaker_status[bus_breaker_idx] else 'aberto'}")
                    print(f"  Disjuntor do gerador (CB{gen_breaker_idx+1}): {'fechado' if self.system.breaker_status[gen_breaker_idx] else 'aberto'}")

    def get_secondary_bus_data(self, idx: int) -> Dict[str, np.ndarray]:
        """
        Get data for a secondary bus for plotting.

        This method returns the time series data for a secondary bus, including
        power, frequency, and voltage values for plotting in the GUI.

        Args:
            idx: Index of the secondary bus

        Returns:
            Dictionary with time, power, frequency, and voltage arrays
        """
        # Get the current time index
        current_idx = self.current_time_index

        # Validate the index
        if idx < 0 or idx >= len(self.system.secondary_buses):
            print(f"Error: Invalid bus index {idx}")
            return {
                "t": np.array([]),
                "power": np.array([]),
                "frequency": np.array([]),
                "voltage": np.array([])
            }

        # Use the previous time index to avoid including the uninitialized value
        if current_idx > 0:
            # Debug info - print data every 100 steps
            if current_idx % 100 == 0:
                voltage_data = self.secondary_bus_voltages[idx][:current_idx]
                print(f"Dados do Barramento {chr(65 + idx)} no passo {current_idx}:")
                print(f"  Pontos de tempo: {len(self.t[:current_idx])}")
                print(f"  Pontos de tensão: {len(voltage_data)}")
                if len(voltage_data) > 0:
                    print(f"  Faixa de tensão: {min(voltage_data)/1000:.2f} - {max(voltage_data)/1000:.2f} kV")

            # Return the data for plotting up to the previous time index
            return {
                "t": self.t[:current_idx],
                "power": self.secondary_bus_powers[idx][:current_idx],
                "frequency": self.secondary_bus_frequencies[idx][:current_idx],
                "voltage": self.secondary_bus_voltages[idx][:current_idx],
                "power_budget": self.secondary_bus_power_budgets[idx][:current_idx]
            }
        else:
            # If we're at the first time index, return empty arrays
            print(f"Bus {chr(65 + idx)} data: Empty (at first time index)")
            return {
                "t": np.array([]),
                "power": np.array([]),
                "frequency": np.array([]),
                "voltage": np.array([]),
                "power_budget": np.array([])
            }

    def get_generator_current_data(self, idx: int) -> np.ndarray:
        """
        Get current data for a specific generator.

        Args:
            idx: Index of the generator

        Returns:
            Array of current values in amperes
        """
        if idx < 0 or idx >= len(self.system.generators):
            print(f"Error: Invalid generator index {idx}")
            return np.array([])

        # Get the current time index
        current_idx = self.current_time_index

        # Use the previous time index to avoid including the uninitialized value
        if current_idx > 0:
            # Retrieve necessary data arrays up to the current index
            p_elec_pu = self.gen_data[idx]["P_elec"][:current_idx]
            v_t_pu = self.gen_data[idx]["V_t"][:current_idx]
            s_base = self.config.s_base
            v_base = self.config.v_base

            # Calculate current, handling potential division by zero
            # Initialize current array with zeros
            current_amp = np.zeros_like(p_elec_pu)
            # Find indices where voltage is non-zero
            non_zero_voltage_indices = v_t_pu != 0
            # Calculate current only for these indices
            current_amp[non_zero_voltage_indices] = (
                p_elec_pu[non_zero_voltage_indices] * s_base
            ) / (v_t_pu[non_zero_voltage_indices] * v_base)

            # Debug info - print minimal current data
            if current_idx % 100 == 0:  # Only print every 100 steps
                print(f"Gerador {idx} calculou dados de corrente para o gráfico no passo {current_idx}")

            return current_amp
        else:
            # If we're at the first time index, return an empty array
            print(f"Dados de corrente do Gerador {idx}: Vazio (no primeiro índice de tempo)")
            return np.array([])

    def is_finished(self) -> bool:
        """
        Check if the simulation has finished.

        Returns:
            True if the simulation has reached the end of the time vector or simulation duration
        """
        if isinstance(self.t, np.ndarray):
            # If t is an array, check if we've reached the end
            finished = self.current_time_index >= len(self.t)
        else:
            # If t is a scalar, check if we've exceeded the simulation duration
            current_time = self.current_time_index * self.config.dt
            finished = current_time >= self.config.simulation_duration
        if finished:
            # Always mark UI update as needed when simulation is finished
            # This ensures the final state is always displayed
            self.ui_update_needed = True

            # Print debug message if this is the first time we're detecting completion
            if self.last_ui_update_index != self.current_time_index - 1:
                print(f"Conclusão da simulação detectada no índice {self.current_time_index}")
                print(f"Última atualização da interface foi no índice {self.last_ui_update_index}")
        return finished

    def is_ui_update_needed(self) -> bool:
        """
        Check if UI update is needed based on update frequency or completion.

        Returns:
            True if UI should be updated
        """
        # Always update if explicitly flagged
        if self.ui_update_needed:
            return True

        # If update frequency is 0, only update at the end
        if self.ui_update_frequency == 0:
            # Only update if simulation just finished
            if self.is_finished() and self.last_ui_update_index != self.current_time_index - 1:
                return True
            return False

        # Update if we've advanced enough steps since last update
        if (self.current_time_index - self.last_ui_update_index) >= self.ui_update_frequency:
            return True

        # Update if simulation just finished
        if self.is_finished() and self.last_ui_update_index != self.current_time_index - 1:
            return True

        return False

    def mark_ui_updated(self) -> None:
        """
        Mark that the UI has been updated at the current time index.
        """
        self.last_ui_update_index = self.current_time_index - 1  # -1 because current_time_index is already incremented
        self.ui_update_needed = False

    def request_ui_update(self) -> None:
        """
        Request a UI update on the next timer tick.
        """
        self.ui_update_needed = True

    def save_data_to_csv(self, filename: str = None) -> str:
        """
        Save all simulation data to a CSV file.

        Args:
            filename: Optional filename to save the data to. If not provided,
                     a filename with the current date will be generated.

        Returns:
            The filename where the data was saved.
        """
        import pandas as pd
        import datetime
        import os

        try:
            # Create a directory for saving data if it doesn't exist
            # Use absolute path to ensure directory is created in the correct location
            script_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(script_dir, "simulation_data")
            os.makedirs(data_dir, exist_ok=True)

            # Generate filename with current date if not provided
            if filename is None:
                current_date = datetime.datetime.now().strftime("%Y-%m-%d")
                filename = f"simulation_{current_date}.csv"

            # Full path to the file
            filepath = os.path.join(data_dir, filename)

            # Get the current time index (ensure it's valid)
            current_idx = min(self.current_time_index, len(self.t))
            if current_idx <= 0:
                raise ValueError("No simulation data available to save")

            # Create a dictionary to store all data
            data = {
                "time": self.t[:current_idx]
            }

            # Add generator data
            for idx, gen in enumerate(self.system.generators):
                gen_name = gen.name
                # Ensure arrays are of the correct length
                p_elec_len = min(current_idx, len(gen.P_elec))
                p_mech_len = min(current_idx, len(gen.P_mech))
                omega_len = min(current_idx, len(gen.omega))
                v_t_len = min(current_idx, len(gen.V_t))

                data[f"Gen_{gen_name}_P_elec"] = gen.P_elec[:p_elec_len]
                data[f"Gen_{gen_name}_P_mech"] = gen.P_mech[:p_mech_len]
                data[f"Gen_{gen_name}_omega"] = gen.omega[:omega_len]
                data[f"Gen_{gen_name}_frequency"] = gen.omega[:omega_len] * self.config.f_base
                data[f"Gen_{gen_name}_V_t"] = gen.V_t[:v_t_len]

            # Add bus data
            for idx, bus in enumerate(self.system.secondary_buses):
                bus_name = chr(65 + idx)  # A, B, C, D
                # Ensure arrays are of the correct length
                power_len = min(current_idx, len(self.secondary_bus_powers[idx]))
                freq_len = min(current_idx, len(self.secondary_bus_frequencies[idx]))
                volt_len = min(current_idx, len(self.secondary_bus_voltages[idx]))

                data[f"Bus_{bus_name}_power"] = self.secondary_bus_powers[idx][:power_len]
                data[f"Bus_{bus_name}_frequency"] = self.secondary_bus_frequencies[idx][:freq_len]
                data[f"Bus_{bus_name}_voltage"] = self.secondary_bus_voltages[idx][:volt_len]

            # Ensure all arrays are the same length by padding with NaN if necessary
            max_len = max(len(arr) for arr in data.values())
            for key in data:
                if len(data[key]) < max_len:
                    # Pad with NaN
                    pad_len = max_len - len(data[key])
                    data[key] = np.append(data[key], [np.nan] * pad_len)

            # Convert to DataFrame and save to CSV
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False)

            print(f"Dados da simulação salvos em {filepath}")
            return filepath

        except Exception as e:
            print(f"Erro ao salvar dados da simulação: {e}")
            import traceback
            traceback.print_exc()
            raise
