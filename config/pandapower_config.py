"""
Configure pandapower settings to avoid segmentation faults.
This module should be imported before importing pandapower.
"""

import os
import warnings

# Disable numba JIT compilation
os.environ["NUMBA_DISABLE_JIT"] = "1"

# Set OpenMP thread limit to 1
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"

# Additional pandapower settings
SOLVER_CONFIG = {
    "algorithm": "nr",  # Newton-Raphson
    "max_iteration": 100,
    "tolerance_mva": 1e-5,  # Increased tolerance for easier convergence
    "enforce_q_lims": True,
    "check_connectivity": True,
    "init": "flat",  # Start from flat voltage profile
    "trafo_model": "t",
    "numba": False,  # Disable numba
    "ac": True,  # Use AC power flow
    "v_debug": True,  # Enable voltage debugging
    "recycle": False,  # Disable solution recycling
    "only_v_results": False,  # Get full results
    "distributed_slack": False,  # Disable distributed slack
    "tdpf": False,  # Disable time domain power flow
    "tdpf_delay_s": None,  # No delay
    "use_umfpack": False,  # Use simpler solver
    "permc_spec": None,  # No permutation
    "lightsim2grid": False  # Don't use lightsim2grid
}

# Suppress pandapower warnings about deprecated features
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
