print("Starting import test...")

try:
    import numpy as np
    print("Numpy imported successfully")
except Exception as e:
    print(f"Error importing numpy: {e}")

try:
    import wx
    print("wxPython imported successfully")
except Exception as e:
    print(f"Error importing wxPython: {e}")

try:
    import matplotlib
    print("Mat<PERSON>lotlib imported successfully")
except Exception as e:
    print(f"Error importing matplotlib: {e}")

try:
    import pandas as pd
    print("Pandas imported successfully")
except Exception as e:
    print(f"Error importing pandas: {e}")

try:
    import scipy
    print("SciPy imported successfully")
except Exception as e:
    print(f"Error importing scipy: {e}")

print("Import test completed")
