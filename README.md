# Simulação de Sistema Elétrico de Potência

Este projeto implementa uma simulação de sistema elétrico de potência com múltiplos geradores e barramentos.

## Características

- Simulação de sistema elétrico de potência com 4 geradores e 5 barramentos (1 principal e 4 secundários)
- Interface gráfica para visualização e controle da simulação
- Configuração flexível através de arquivo JSON
- Visualização de gráficos de potência, frequência, tensão e corrente
- Controle de disjuntores para alterar a topologia do sistema

## Requisitos

- Python 3.10 ou superior
- Bibliotecas: numpy, matplotlib, wxPython

## Instalação

```bash
pip install scipy==1.11.4 numpy==1.24.3 pandas==2.0.3 matplotlib==3.7.2 wxPython==4.2.1
```

## Execução

```bash
python main.py
```

## Configuração

O sistema pode ser configurado através do arquivo `config.json`. Para instruções detalhadas sobre como configurar o sistema, consulte o arquivo [config_instructions.md](config_instructions.md).

## Estrutura do Projeto

- `main.py`: Ponto de entrada principal da aplicação
- `core/`: Módulos principais da simulação
  - `generator.py`: Implementação dos modelos de geradores
  - `simulation.py`: Implementação da simulação do sistema elétrico
  - `constants.py`: Constantes e configurações do sistema
- `gui/`: Módulos da interface gráfica
  - `gui_fixed.py`: Implementação da interface gráfica principal
  - `generator_info_panel.py`: Painel de informações dos geradores
  - `generator_config_dialog.py`: Diálogo de configuração dos geradores
  - `current_values_dialog.py`: Diálogo de valores atuais
  - `turbine_params_panel.py`: Painel de parâmetros da turbina
- `config/`: Arquivos de configuração
  - `config.json`: Configuração principal do sistema
  - `pandapower_config.py`: Configuração do pandapower (se utilizado)

## Licença

Este projeto é distribuído sob a licença MIT.
