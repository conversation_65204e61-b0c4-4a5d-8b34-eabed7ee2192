2025-06-03 22:21:09 - INFO - Sistema de registro de logs configurado
2025-06-03 22:21:09 - INFO - Sistema de registro de logs configurado
2025-06-03 22:21:09 - INFO - Iniciando simulação do sistema elétrico de potência...
2025-06-03 22:21:09 - INFO - Criando sistema elétrico de potência...
2025-06-03 22:21:09 - ERROR - Erro ao criar sistema elétrico de potência: 'SimplifiedNetwork' object has no attribute 'create_load'
2025-06-03 22:21:09 - ERROR - 
Parece haver um problema com a biblioteca pandapower.
Por favor, tente as seguintes soluções:
1. Instale versões compatíveis das dependências:
   pip install scipy==1.11.4 numpy==1.24.3 pandas==2.0.3 matplotlib==3.7.2 wxPython==4.2.1 pandapower==2.13.1
2. Crie um novo ambiente conda com Python 3.10 ou 3.11:
   conda create -n powerenv python=3.10
   conda activate powerenv
   pip install scipy==1.11.4 numpy==1.24.3 pandas==2.0.3 matplotlib==3.7.2 wxPython==4.2.1 pandapower==2.13.1
3. Se tudo mais falhar, pode ser necessário modificar o arquivo simulation.py para evitar o uso do pandapower.
   Veja o arquivo fix_instructions.txt para mais detalhes.
2025-06-03 22:21:09 - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Documents/python/wxpython-ex/project/main.py", line 691, in main
    system = PowerSystem(config, generators, breaker_status)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/python/wxpython-ex/project/core/simulation.py", line 319, in __init__
    self._initialize_pandapower_network()
  File "/Users/<USER>/Documents/python/wxpython-ex/project/core/simulation.py", line 423, in _initialize_pandapower_network
    self.net.create_load(
    ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SimplifiedNetwork' object has no attribute 'create_load'. Did you mean: 'create_line'?

